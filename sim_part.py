


import stim
import stimcirq
from stim import PauliString, <PERSON>au
from typing import Iterable, <PERSON>ple

import random
import numpy as np
import time

from qiskit import QuantumCircuit
from qiskit_aer import AerSimulator

# from qasm import test_qasm1

test_qasm1 = """
OPENQASM 2.0;
include "qelib1.inc";

qreg q[3];
h q[0];
cx q[0], q[1];
cx q[1], q[2];
s q[2];
h q[2];
"""

def extract_stabilizers_from_qasm(qasm: str):
    
    print("Option 2--------------------------------")
    start_time = time.time()

    circuit = QuantumCircuit.from_qasm_str(qasm)
    print(f"Circuit Elapsed Time: {time.time() - start_time}")
    
    start_time = time.time()
    circuit_no_meas = circuit #circuit_no_meas = circuit.remove_final_measurements(inplace=False) # 

    device = "GPU"
    backend_sv = AerSimulator(method="statevector", device=device, blocking_enable=True)
    circuit_no_meas.save_statevector()  # type: ignore
    job = backend_sv.run(circuit_no_meas)
    result = job.result()
    statevector = result.data(0)["statevector"]
    statevector = np.array(statevector)

    print(f"StateVector Elapsed Time: {time.time() - start_time}")

    # Step 3: Simulate with TableauSimulator
    tableau = Tableau.from_state_vector(statevector, endian="little")
    stabilizers = tableau.to_stabilizers(canonicalize=True)
    num_qubits = int(np.log2(len(statevector)))
    
    print(f"Circuit Elapsed Time: {time.time() - start_time}")
    
    return {
        "stabilizers": stabilizers,
        "num_qubits": num_qubits,
        "success": True,
    }

def extract_peaked_from_qasm(qasm: str):
    
    print("Option 2--------------------------------")
    start_time = time.time()

    circuit = QuantumCircuit.from_qasm_str(qasm)
    print(f"Circuit Elapsed Time: {time.time() - start_time}")
    
    start_time = time.time()
    circuit_no_meas = circuit #circuit_no_meas = circuit.remove_final_measurements(inplace=False) # 

    device = "GPU"
    backend_sv = AerSimulator(method="statevector", device=device, blocking_enable=True)
    circuit_no_meas.save_statevector()  # type: ignore
    job = backend_sv.run(circuit_no_meas)
    result = job.result()
    statevector = result.data(0)["statevector"]
    statevector = np.array(statevector)

    print(f"StateVector Elapsed Time: {time.time() - start_time}")

    n_qubits = int(np.log2(len(statevector)))

    probabilities = np.abs(statevector) ** 2

    peak_idx = np.argmax(probabilities)
    peak_probability = probabilities[peak_idx]

    raw_bitstring = format(peak_idx, f"0{n_qubits}b")
    peak_bitstring = raw_bitstring[::-1]

    top_indices = np.argsort(probabilities)[-5:][::-1]
    print("Top 5 bitstrings by probability:")
    for idx in top_indices:
        raw_bitstr = format(idx, f"0{n_qubits}b")
        bitstr = raw_bitstr[::-1]
        prob = probabilities[idx]
        print(f"  {bitstr}: {prob:g}")
    peaking = probabilities[top_indices[0]] / probabilities[top_indices[1]]

    print(f"Peak Elapsed Time: {time.time() - start_time}")

    return {
        "peak_bitstring": peak_bitstring,
        "peak_probability": float(peak_probability),
        "peaking_ratio": peaking,
    }
    
    
    

# Run
    
def flow_work():
    
    stabilizers = extract_stabilizers_from_qasm(test_qasm1)
    print(stabilizers)
    
    peak = extract_peaked_from_qasm(test_qasm1)
    print(peak)
    
flow_work()

